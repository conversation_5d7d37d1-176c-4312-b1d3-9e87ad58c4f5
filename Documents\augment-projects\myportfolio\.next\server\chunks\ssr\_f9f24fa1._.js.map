{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/myportfolio/src/app/page.tsx"], "sourcesContent": ["export default function Home() {\n  return (\n    <div className=\"min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-white\">\n      {/* Header Section */}\n      <header className=\"text-center py-20\">\n        <h1 className=\"text-4xl font-bold mb-4\">Pranav <PERSON></h1>\n        <p className=\"text-xl text-gray-600 dark:text-gray-300\">Full Stack Developer</p>\n      </header>\n\n      {/* About Section */}\n      <section className=\"max-w-4xl mx-auto px-6 py-16\">\n        <h2 className=\"text-3xl font-bold mb-8\">About Me</h2>\n        <p className=\"text-lg leading-relaxed\">\n          I'm a passionate Full Stack Developer with expertise in Java and MERN stack technologies. \n          I enjoy building scalable web applications and working with cloud computing solutions. \n          Always eager to learn new technologies and solve complex problems.\n        </p>\n      </section>\n\n      {/* Skills Section */}\n      <section className=\"max-w-4xl mx-auto px-6 py-16\">\n        <h2 className=\"text-3xl font-bold mb-8\">Skills</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          {/* Java Full Stack */}\n          <div className=\"bg-blue-100 dark:bg-blue-900 p-6 rounded-lg\">\n            <h3 className=\"text-xl font-semibold mb-3 text-blue-800 dark:text-blue-200\">Java Full Stack</h3>\n            <ul className=\"space-y-2\">\n              <li>• Java</li>\n              <li>• Spring Boot</li>\n              <li>• Spring Framework</li>\n              <li>• Hibernate/JPA</li>\n              <li>• MySQL/PostgreSQL</li>\n            </ul>\n          </div>\n\n          {/* MERN Stack */}\n          <div className=\"bg-green-100 dark:bg-green-900 p-6 rounded-lg\">\n            <h3 className=\"text-xl font-semibold mb-3 text-green-800 dark:text-green-200\">MERN Stack</h3>\n            <ul className=\"space-y-2\">\n              <li>• MongoDB</li>\n              <li>• Express.js</li>\n              <li>• React.js</li>\n              <li>• Node.js</li>\n              <li>• JavaScript/TypeScript</li>\n            </ul>\n          </div>\n\n          {/* Cloud Computing */}\n          <div className=\"bg-purple-100 dark:bg-purple-900 p-6 rounded-lg\">\n            <h3 className=\"text-xl font-semibold mb-3 text-purple-800 dark:text-purple-200\">Cloud Computing</h3>\n            <ul className=\"space-y-2\">\n              <li>• AWS</li>\n              <li>• Docker</li>\n              <li>• Kubernetes</li>\n              <li>• CI/CD</li>\n              <li>• Microservices</li>\n            </ul>\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Section */}\n      <section className=\"max-w-4xl mx-auto px-6 py-16\">\n        <h2 className=\"text-3xl font-bold mb-8\">Contact</h2>\n        <div className=\"bg-gray-100 dark:bg-gray-800 p-6 rounded-lg\">\n          <p className=\"text-lg\">\n            <span className=\"font-semibold\">Email:</span> <EMAIL>\n          </p>\n          <p className=\"text-gray-600 dark:text-gray-300 mt-2\">\n            Feel free to reach out for collaboration opportunities or just to connect!\n          </p>\n        </div>\n      </section>\n    </div>\n  );\n}\n\n\n\n\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAA2C;;;;;;;;;;;;0BAI1D,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;0BAQzC,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA8D;;;;;;kDAC5E,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAKR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAgE;;;;;;kDAC9E,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAKR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAkE;;;;;;kDAChF,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOZ,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAa;;;;;;;0CAE/C,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;;;;;;;;;;;;;AAO/D", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/myportfolio/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/myportfolio/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}