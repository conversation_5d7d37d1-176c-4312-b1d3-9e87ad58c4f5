"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "HeadManagerContext", {
    enumerable: true,
    get: function() {
        return HeadManagerContext;
    }
});
const _interop_require_default = require("@swc/helpers/_/_interop_require_default");
const _react = /*#__PURE__*/ _interop_require_default._(require("react"));
const HeadManagerContext = _react.default.createContext({});
if (process.env.NODE_ENV !== 'production') {
    HeadManagerContext.displayName = 'HeadManagerContext';
}

//# sourceMappingURL=head-manager-context.shared-runtime.js.map