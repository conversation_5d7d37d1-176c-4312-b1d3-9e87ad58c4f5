export default function Home() {
  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
      {/* Header Section */}
      <header className="text-center py-20">
        <h1 className="text-4xl font-bold mb-4">Pranav <PERSON></h1>
        <p className="text-xl text-gray-600 dark:text-gray-300">Full Stack Developer</p>
      </header>

      {/* About Section */}
      <section className="max-w-4xl mx-auto px-6 py-16">
        <h2 className="text-3xl font-bold mb-8">About Me</h2>
        <p className="text-lg leading-relaxed">
          I'm a passionate Full Stack Developer with expertise in Java and MERN stack technologies. 
          I enjoy building scalable web applications and working with cloud computing solutions. 
          Always eager to learn new technologies and solve complex problems.
        </p>
      </section>

      {/* Skills Section */}
      <section className="max-w-4xl mx-auto px-6 py-16">
        <h2 className="text-3xl font-bold mb-8">Skills</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Java Full Stack */}
          <div className="bg-blue-100 dark:bg-blue-900 p-6 rounded-lg">
            <h3 className="text-xl font-semibold mb-3 text-blue-800 dark:text-blue-200">Java Full Stack</h3>
            <ul className="space-y-2">
              <li>• Java</li>
              <li>• Spring Boot</li>
              <li>• Spring Framework</li>
              <li>• Hibernate/JPA</li>
              <li>• MySQL/PostgreSQL</li>
            </ul>
          </div>

          {/* MERN Stack */}
          <div className="bg-green-100 dark:bg-green-900 p-6 rounded-lg">
            <h3 className="text-xl font-semibold mb-3 text-green-800 dark:text-green-200">MERN Stack</h3>
            <ul className="space-y-2">
              <li>• MongoDB</li>
              <li>• Express.js</li>
              <li>• React.js</li>
              <li>• Node.js</li>
              <li>• JavaScript/TypeScript</li>
            </ul>
          </div>

          {/* Cloud Computing */}
          <div className="bg-purple-100 dark:bg-purple-900 p-6 rounded-lg">
            <h3 className="text-xl font-semibold mb-3 text-purple-800 dark:text-purple-200">Cloud Computing</h3>
            <ul className="space-y-2">
              <li>• AWS</li>
              <li>• Docker</li>
              <li>• Kubernetes</li>
              <li>• CI/CD</li>
              <li>• Microservices</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="max-w-4xl mx-auto px-6 py-16">
        <h2 className="text-3xl font-bold mb-8">Contact</h2>
        <div className="bg-gray-100 dark:bg-gray-800 p-6 rounded-lg">
          <p className="text-lg">
            <span className="font-semibold">Email:</span> <EMAIL>
          </p>
          <p className="text-gray-600 dark:text-gray-300 mt-2">
            Feel free to reach out for collaboration opportunities or just to connect!
          </p>
        </div>
      </section>
    </div>
  );
}



