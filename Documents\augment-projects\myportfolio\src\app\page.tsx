export default function Home() {
  return (
    <div className="min-h-screen bg-white text-gray-900">
      {/* Header Section */}
      <header className="text-center py-20">
        <h1 className="text-4xl font-bold mb-4">Prana<PERSON></h1>
        <p className="text-xl text-gray-600">Full Stack Developer</p>
      </header>

      {/* About Section */}
      <section className="max-w-4xl mx-auto px-6 py-16">
        <h2 className="text-3xl font-bold mb-8">About Me</h2>
        <p className="text-lg leading-relaxed">
          I'm a passionate Full Stack Developer with expertise in Java and MERN stack technologies. 
          I enjoy building scalable web applications and working with cloud computing solutions. 
          Always eager to learn new technologies and solve complex problems.
        </p>
      </section>

      {/* Skills Section */}
      <section className="max-w-4xl mx-auto px-6 py-16">
        <h2 className="text-3xl font-bold mb-8">Skills</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Java Full Stack */}
          <div className="bg-blue-100 p-6 rounded-lg border border-blue-200">
            <h3 className="text-xl font-semibold mb-3 text-blue-800">Java Full Stack</h3>
            <ul className="space-y-2 text-gray-700">
              <li>• Java</li>
              <li>• Spring Boot</li>
              <li>• Spring Framework</li>
              <li>• Hibernate/JPA</li>
              <li>• MySQL/PostgreSQL</li>
            </ul>
          </div>

          {/* MERN Stack */}
          <div className="bg-green-100 p-6 rounded-lg border border-green-200">
            <h3 className="text-xl font-semibold mb-3 text-green-800">MERN Stack</h3>
            <ul className="space-y-2 text-gray-700">
              <li>• MongoDB</li>
              <li>• Express.js</li>
              <li>• React.js</li>
              <li>• Node.js</li>
              <li>• JavaScript/TypeScript</li>
            </ul>
          </div>

          {/* Cloud Computing */}
          <div className="bg-purple-100 p-6 rounded-lg border border-purple-200">
            <h3 className="text-xl font-semibold mb-3 text-purple-800">Cloud Computing</h3>
            <ul className="space-y-2 text-gray-700">
              <li>• AWS</li>
              <li>• Docker</li>
              <li>• Kubernetes</li>
              <li>• CI/CD</li>
              <li>• Microservices</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="max-w-4xl mx-auto px-6 py-16">
        <h2 className="text-3xl font-bold mb-8">Contact</h2>
        <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
          <p className="text-lg">
            <span className="font-semibold">Email:</span> <EMAIL>
          </p>
          <p className="text-gray-600 mt-2">
            Feel free to reach out for collaboration opportunities or just to connect!
          </p>
        </div>
      </section>
    </div>
  );
}







