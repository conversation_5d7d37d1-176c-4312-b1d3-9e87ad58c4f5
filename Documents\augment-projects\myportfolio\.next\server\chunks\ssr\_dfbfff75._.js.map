{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/myportfolio/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-white text-gray-900\">\n      {/* Header Section with Photo */}\n      <header className=\"text-center py-20\">\n        <div className=\"mb-8\">\n          <Image\n            src=\"/pranav-photo.jpg\"\n            alt=\"Pranav Santosh Jagadale\"\n            width={200}\n            height={200}\n            className=\"rounded-full mx-auto border-4 border-blue-500 shadow-lg\"\n            priority\n          />\n        </div>\n        <h1 className=\"text-4xl font-bold mb-4\">Pranav Santosh Jagadale</h1>\n        <p className=\"text-xl text-gray-600\">Full Stack Developer</p>\n      </header>\n\n      {/* About Section */}\n      <section className=\"max-w-4xl mx-auto px-6 py-16\">\n        <h2 className=\"text-3xl font-bold mb-8\">About Me</h2>\n        <p className=\"text-lg leading-relaxed\">\n          I'm a passionate Full Stack Developer with expertise in Java and MERN stack technologies. \n          I enjoy building scalable web applications and working with cloud computing solutions. \n          Always eager to learn new technologies and solve complex problems.\n        </p>\n      </section>\n\n      {/* Skills Section */}\n      <section className=\"max-w-4xl mx-auto px-6 py-16\">\n        <h2 className=\"text-3xl font-bold mb-8\">Skills</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          {/* Java Full Stack */}\n          <div className=\"bg-blue-100 p-6 rounded-lg border border-blue-200\">\n            <h3 className=\"text-xl font-semibold mb-3 text-blue-800\">Java Full Stack</h3>\n            <ul className=\"space-y-2 text-gray-700\">\n              <li>• Java</li>\n              <li>• Spring Boot</li>\n              <li>• Spring Framework</li>\n              <li>• Hibernate/JPA</li>\n              <li>• MySQL/PostgreSQL</li>\n            </ul>\n          </div>\n\n          {/* MERN Stack */}\n          <div className=\"bg-green-100 p-6 rounded-lg border border-green-200\">\n            <h3 className=\"text-xl font-semibold mb-3 text-green-800\">MERN Stack</h3>\n            <ul className=\"space-y-2 text-gray-700\">\n              <li>• MongoDB</li>\n              <li>• Express.js</li>\n              <li>• React.js</li>\n              <li>• Node.js</li>\n              <li>• JavaScript/TypeScript</li>\n            </ul>\n          </div>\n\n          {/* Cloud Computing */}\n          <div className=\"bg-purple-100 p-6 rounded-lg border border-purple-200\">\n            <h3 className=\"text-xl font-semibold mb-3 text-purple-800\">Cloud Computing</h3>\n            <ul className=\"space-y-2 text-gray-700\">\n              <li>• AWS</li>\n              <li>• Docker</li>\n              <li>• Kubernetes</li>\n              <li>• CI/CD</li>\n              <li>• Microservices</li>\n            </ul>\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Section */}\n      <section className=\"max-w-4xl mx-auto px-6 py-16\">\n        <h2 className=\"text-3xl font-bold mb-8\">Contact</h2>\n        <div className=\"bg-gray-50 p-6 rounded-lg border border-gray-200\">\n          <p className=\"text-lg\">\n            <span className=\"font-semibold\">Email:</span> <EMAIL>\n          </p>\n          <p className=\"text-gray-600 mt-2\">\n            Feel free to reach out for collaboration opportunities or just to connect!\n          </p>\n        </div>\n      </section>\n    </div>\n  );\n}\n\n\n\n\n\n\n\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;4BACV,QAAQ;;;;;;;;;;;kCAGZ,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAIvC,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;0BAQzC,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAKR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAKR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6C;;;;;;kDAC3D,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOZ,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAa;;;;;;;0CAE/C,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}]}